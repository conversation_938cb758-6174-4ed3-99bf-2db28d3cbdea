# Augment VIP - PyQt6版本

这是Augment VIP项目的PyQt6重构版本，将UI代码与业务逻辑完全分离，提供更好的代码组织和维护性。

## 项目结构

```
augment-vip-main/
├── main.py                # 主程序（PyQt6）
├── main_window_ui.py      # 主窗口UI类
├── email_monitor_ui.py    # 邮箱监控窗口UI类
├── core_functions.py      # 核心业务逻辑
├── requirements.txt       # 依赖配置
├── pack.py               # 打包脚本
├── README_PyQt6.md       # 项目说明
└── dist/                 # 打包输出目录
```

## 新版本特性

### 1. UI与业务逻辑分离
- **main_window_ui.py**: 主窗口的UI布局和样式
- **email_monitor_ui.py**: 邮箱监控窗口的UI布局和样式
- **core_functions.py**: 所有业务逻辑（VS Code清理、遥测ID修改、邮箱监控等）

### 2. 现代化的PyQt6界面
- 使用PyQt6替代tkinter，提供更现代的界面
- 自定义样式和主题
- 更好的响应式布局
- 改进的用户体验

### 3. 模块化设计
- 每个UI组件都是独立的类
- 通过信号和槽机制进行通信
- 便于测试和维护

## 安装和运行

### 安装依赖
```bash
pip install PyQt6 beautifulsoup4 lxml
```

### 运行应用程序
```bash
# 运行应用程序
python main.py
```

## 功能说明

### 主窗口功能
1. **一键重置**: 清理VS Code数据库中的Augment相关条目并生成新的遥测ID
2. **邮箱接收**: 打开邮箱验证码监控工具
3. **操作日志**: 实时显示操作过程和结果

### 邮箱监控功能
1. **登录信息管理**: 支持保存和加载邮箱账号密码
2. **随机账户生成**: 自动生成随机邮箱账户
3. **验证码监控**: 自动监控新邮件并提取验证码
4. **自动复制**: 可选择自动复制验证码到剪贴板
5. **网站快捷访问**: 一键打开Augment Code网站

## 技术架构

### UI层 (main_window_ui.py, email_monitor_ui.py)
- 负责界面布局和样式
- 定义信号用于与业务逻辑通信
- 提供用户交互接口

### 业务逻辑层 (core_functions.py)
- VS Code数据库清理
- 遥测ID生成和修改
- 邮箱监控和验证码提取
- 配置管理（Windows注册表）

### 应用层 (main.py)
- 整合UI和业务逻辑
- 处理信号和槽连接
- 管理应用程序生命周期

## 开发说明

### 添加新功能
1. 在`core_functions.py`中添加业务逻辑
2. 在相应的UI文件中添加界面元素
3. 在主程序中连接信号和槽

### 修改界面
- 主窗口: 编辑`main_window_ui.py`
- 邮箱监控: 编辑`email_monitor_ui.py`

### 样式定制
UI文件中包含了完整的样式定义，可以通过修改`setStyleSheet()`调用来自定义外观。

## 兼容性

- **操作系统**: Windows（主要支持）
- **Python版本**: 3.7+
- **依赖**: PyQt6, beautifulsoup4, lxml

## 注意事项

1. 使用PyQt6构建的现代化GUI界面
2. 配置文件存储在Windows注册表中
3. 支持VS Code数据库清理和遥测ID修改
4. 内置邮箱验证码监控功能

## 更新日志

### v2.0.0 (PyQt6版本)
- 完全重构为PyQt6架构
- UI与业务逻辑分离
- 改进的界面设计和用户体验
- 模块化代码结构
- 更好的错误处理和日志记录

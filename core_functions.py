#!/usr/bin/env python3
"""
核心业务逻辑模块
包含VS Code清理、遥测ID修改、邮箱监控等核心功能
"""

import os
import json
import sqlite3
import uuid
import shutil
import queue
import time
import re
import base64
import subprocess
import winreg
from pathlib import Path
from typing import Dict
from datetime import datetime

# 邮箱相关导入
try:
    import poplib
    import email
    from bs4 import BeautifulSoup
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False

# 版本信息
__version__ = "2.0.0"

# 邮箱配置
POP3_SERVER = 'pop.2925.com'
POP3_PORT = 110
REFRESH_INTERVAL_SECONDS = 1


def _create_logger(log_callback):
    """创建日志函数"""
    def log(message, level="INFO"):
        if log_callback:
            log_callback(message, level)
        else:
            print(f"[{level}] {message}")
    return log


def get_vscode_paths() -> Dict[str, Path]:
    """
    获取Windows系统下VS Code的路径
    
    Returns:
        包含VS Code目录和文件路径的字典
    """
    appdata = os.environ.get("APPDATA")
    if not appdata:
        raise Exception("未找到APPDATA环境变量")
    
    base_dir = Path(appdata) / "Code" / "User"
    
    paths = {
        "base_dir": base_dir,
        "storage_json": base_dir / "globalStorage" / "storage.json",
        "state_db": base_dir / "globalStorage" / "state.vscdb"
    }
    
    return paths


def backup_file(file_path: Path) -> Path:
    """
    创建文件备份
    
    Args:
        file_path: 要备份的文件路径
        
    Returns:
        备份文件路径
    """
    if not file_path.exists():
        raise FileNotFoundError(f"文件不存在: {file_path}")
        
    backup_path = Path(f"{file_path}.backup")
    shutil.copy2(file_path, backup_path)
    
    return backup_path


def generate_machine_id() -> str:
    """生成64字符的十六进制字符串作为machineId"""
    return uuid.uuid4().hex + uuid.uuid4().hex


def generate_device_id() -> str:
    """生成UUID v4作为devDeviceId"""
    return str(uuid.uuid4())


def clean_vscode_db(log_callback=None) -> bool:
    """
    清理VS Code数据库，删除包含"augment"的条目

    Args:
        log_callback: 日志回调函数

    Returns:
        成功返回True，失败返回False
    """
    log = _create_logger(log_callback)
    log("开始数据库清理过程")
    
    try:
        # 获取VS Code路径
        paths = get_vscode_paths()
        state_db = paths["state_db"]
        
        if not state_db.exists():
            log(f"未找到VS Code数据库: {state_db}", "WARNING")
            return False
        
        log(f"找到VS Code数据库: {state_db}")
        
        # 创建备份
        backup_path = backup_file(state_db)
        log(f"已创建备份: {backup_path}", "SUCCESS")
        
        # 连接数据库
        conn = sqlite3.connect(str(state_db))
        cursor = conn.cursor()
        
        # 获取删除前的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_before = cursor.fetchone()[0]
        
        if count_before == 0:
            log("数据库中未找到Augment相关条目")
            conn.close()
            return True
        
        # 删除包含"augment"的记录
        cursor.execute("DELETE FROM ItemTable WHERE key LIKE '%augment%'")
        conn.commit()
        
        # 获取删除后的记录数
        cursor.execute("SELECT COUNT(*) FROM ItemTable WHERE key LIKE '%augment%'")
        count_after = cursor.fetchone()[0]
        
        conn.close()
        
        log(f"已从数据库中删除 {count_before - count_after} 个Augment相关条目", "SUCCESS")
        return True
        
    except sqlite3.Error as e:
        log(f"SQLite错误: {e}", "ERROR")
        
        # 如果出错，从备份恢复
        try:
            if 'backup_path' in locals() and backup_path.exists():
                log("正在从备份恢复...")
                shutil.copy2(backup_path, state_db)
                log("已从备份恢复", "SUCCESS")
        except Exception as restore_error:
            log(f"从备份恢复失败: {restore_error}", "ERROR")
        
        return False
    except Exception as e:
        log(f"意外错误: {e}", "ERROR")
        return False


def modify_telemetry_ids(log_callback=None) -> bool:
    """
    修改VS Code storage.json文件中的遥测ID

    Args:
        log_callback: 日志回调函数

    Returns:
        成功返回True，失败返回False
    """
    log = _create_logger(log_callback)
    log("开始VS Code遥测ID修改")
    
    try:
        # 获取VS Code路径
        paths = get_vscode_paths()
        storage_json = paths["storage_json"]
        
        if not storage_json.exists():
            log(f"未找到VS Code storage.json: {storage_json}", "WARNING")
            return False
        
        log(f"找到storage.json: {storage_json}")
        
        # 创建备份
        backup_path = backup_file(storage_json)
        log(f"已创建备份: {backup_path}", "SUCCESS")
        
        # 生成新ID
        log("正在生成新的遥测ID...")
        machine_id = generate_machine_id()
        device_id = generate_device_id()
        
        # 读取当前文件
        with open(storage_json, 'r', encoding='utf-8') as f:
            content = json.load(f)
        
        # 更新值
        content["telemetry.machineId"] = machine_id
        content["telemetry.devDeviceId"] = device_id
        
        # 写回文件
        with open(storage_json, 'w', encoding='utf-8') as f:
            json.dump(content, f, indent=2)
        
        log("成功更新遥测ID", "SUCCESS")
        log(f"新的machineId: {machine_id}")
        log(f"新的devDeviceId: {device_id}")
        log("您可能需要重启VS Code以使更改生效")
        
        return True
        
    except json.JSONDecodeError:
        log("存储文件不是有效的JSON格式", "ERROR")
        return False
    except Exception as e:
        log(f"意外错误: {e}", "ERROR")
        return False


class ConfigManager:
    """配置管理器 - 使用Windows注册表"""
    
    def __init__(self):
        self.registry_key = r"SOFTWARE\AugmentVIP\EmailMonitor"
    
    def save_config(self, email_account, email_password, auto_copy=True):
        """保存配置到Windows注册表"""
        try:
            # 创建或打开注册表键
            key = winreg.CreateKey(winreg.HKEY_CURRENT_USER, self.registry_key)
            
            # 保存邮箱账号
            winreg.SetValueEx(key, "account", 0, winreg.REG_SZ, email_account)
            
            # 保存密码（base64编码）
            if email_password:
                encoded_password = base64.b64encode(email_password.encode('utf-8')).decode('utf-8')
                winreg.SetValueEx(key, "password", 0, winreg.REG_SZ, encoded_password)
            else:
                winreg.SetValueEx(key, "password", 0, winreg.REG_SZ, "")
            
            # 保存选项设置
            winreg.SetValueEx(key, "auto_copy", 0, winreg.REG_DWORD, 1 if auto_copy else 0)
            
            winreg.CloseKey(key)
            return True
            
        except Exception:
            return False
    
    def load_config(self):
        """从Windows注册表加载配置"""
        try:
            # 打开注册表键
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, self.registry_key)
            
            config = {}
            
            # 加载邮箱账号
            try:
                account, _ = winreg.QueryValueEx(key, "account")
                config['account'] = account
            except FileNotFoundError:
                config['account'] = ""
            
            # 加载密码（base64解码）
            try:
                encoded_password, _ = winreg.QueryValueEx(key, "password")
                if encoded_password:
                    try:
                        decoded_password = base64.b64decode(encoded_password).decode('utf-8')
                        config['password'] = decoded_password
                    except:
                        config['password'] = ""
                else:
                    config['password'] = ""
            except FileNotFoundError:
                config['password'] = ""
            
            # 加载选项设置
            try:
                auto_copy, _ = winreg.QueryValueEx(key, "auto_copy")
                config['auto_copy'] = bool(int(auto_copy))
            except FileNotFoundError:
                config['auto_copy'] = True
            
            winreg.CloseKey(key)
            return config
            
        except FileNotFoundError:
            # 注册表键不存在，返回默认值
            return {
                'account': '',
                'password': '',
                'auto_copy': True
            }
        except Exception:
            # 如果加载配置失败，返回默认值
            return {
                'account': '',
                'password': '',
                'auto_copy': True
            }


# 邮箱相关辅助函数
def decode_payload(payload, charset):
    """安全地用给定字符集解码负载"""
    try:
        return payload.decode(charset)
    except (UnicodeDecodeError, LookupError):
        return payload.decode('gbk', errors='ignore')


def get_clean_body_from_msg(msg):
    """解析email.message对象并返回纯净的文本正文"""
    if not EMAIL_AVAILABLE:
        return ""

    body_content, html_content = "", ""
    if msg.is_multipart():
        for part in msg.walk():
            if part.get('Content-Disposition', '').startswith('attachment'):
                continue
            payload = part.get_payload(decode=True)
            if not payload:
                continue
            charset = part.get_content_charset() or 'utf-8'
            content_type = part.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)
    else:
        if not msg.get('Content-Disposition', '').startswith('attachment'):
            payload = msg.get_payload(decode=True)
            charset = msg.get_content_charset() or 'utf-8'
            content_type = msg.get_content_type()
            if content_type == 'text/plain':
                body_content = decode_payload(payload, charset)
            elif content_type == 'text/html':
                html_content = decode_payload(payload, charset)

    if not body_content.strip() and html_content:
        try:
            soup = BeautifulSoup(html_content, 'lxml')
            return soup.get_text(separator='\n', strip=True)
        except:
            return html_content
    return body_content


def find_code_in_text(body_text):
    """使用正则表达式在字符串中查找6位验证码"""
    patterns = [r'\b\d{6}\b', r'\b\d{3}\s\d{3}\b', r'\b(?:\d\s){5}\d\b']
    for pattern in patterns:
        match = re.search(pattern, body_text)
        if match:
            return match.group(0).replace(" ", "")
    return None


def establish_baseline(server, output_queue):
    """获取当前所有邮件的UIDL，建立基线"""
    try:
        resp, uid_lines, octets = server.uidl()
        seen_uids = {line.split()[1] for line in uid_lines}
        output_queue.put(f"基线已建立，当前有 {len(seen_uids)} 封邮件。开始监控新邮件...")
        return seen_uids
    except Exception as e:
        output_queue.put(f"建立基线时出错: {e}")
        return None


def email_monitor_main(email_account, email_password, output_queue):
    """邮箱监控主逻辑"""
    if not EMAIL_AVAILABLE:
        output_queue.put("错误：缺少邮箱相关依赖包")
        return

    output_queue.put(f"正在监控邮箱: {email_account}")

    try:
        server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
        server.user(email_account)
        server.pass_(email_password)
        output_queue.put("验证成功。")
        seen_uids = establish_baseline(server, output_queue)
        server.quit()
        if seen_uids is None:
            return
    except poplib.error_proto as e:
        output_queue.put(f"错误：登录失败。请检查凭据。 ({e})")
        return
    except Exception as e:
        output_queue.put(f"连接或建立基线时发生未知错误: {e}")
        return

    loop_counter = 0
    while True:
        try:
            try:
                if output_queue.get_nowait() == 'STOP':
                    break
            except queue.Empty:
                pass

            server = poplib.POP3(POP3_SERVER, port=POP3_PORT, timeout=20)
            server.user(email_account)
            server.pass_(email_password)

            resp, uid_lines, octets = server.uidl()

            current_uid_map = {
                parts[1]: parts[0]
                for line in uid_lines
                if len(parts := line.split()) == 2
            }

            new_uids = set(current_uid_map.keys()) - seen_uids

            if new_uids:
                loop_counter = 0
                output_queue.put(f"\n发现 {len(new_uids)} 封新邮件，正在检查...")
                new_messages = sorted(
                    [(int(current_uid_map[uid]), uid) for uid in new_uids],
                    key=lambda x: x[0],
                    reverse=True
                )

                for msg_num, uid in new_messages:
                    resp, lines, octets = server.retr(msg_num)
                    msg_content = b'\r\n'.join(lines)
                    msg = email.message_from_bytes(msg_content)

                    body = get_clean_body_from_msg(msg)
                    code = find_code_in_text(body)

                    if code:
                        output_queue.put(f"成功提取到新邮件中的验证码: {code}")
                        output_queue.put(f"VERIFICATION_CODE:{code}")
                        server.quit()
                        return

                output_queue.put("新邮件中未发现验证码，将继续监控...")
                seen_uids.update(new_uids)
            else:
                loop_counter += 1
                if loop_counter % 15 == 1:
                    output_queue.put(f"没有新邮件，继续监控... ({time.strftime('%H:%M:%S')})")

            server.quit()
            time.sleep(REFRESH_INTERVAL_SECONDS)

        except KeyboardInterrupt:
            output_queue.put("\n程序已手动停止。")
            break
        except Exception as e:
            output_queue.put(f"\n监控循环中发生错误: {e}。等待10秒后重试...")
            time.sleep(10)


def open_website_in_chrome():
    """在谷歌浏览器无痕模式下打开网站"""
    try:
        url = "https://www.augmentcode.com/"

        # 尝试不同的Chrome路径
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
            "chrome"  # 如果Chrome在PATH中
        ]

        chrome_found = False
        for chrome_path in chrome_paths:
            try:
                if chrome_path == "chrome" or os.path.exists(chrome_path):
                    # 使用无痕模式打开网站
                    subprocess.Popen([chrome_path, "--incognito", url])
                    chrome_found = True
                    return "已在Chrome无痕模式下打开 Augment Code 网站"
            except:
                continue

        if not chrome_found:
            # 如果找不到Chrome，使用默认浏览器
            import webbrowser
            webbrowser.open(url)
            return "已在默认浏览器中打开 Augment Code 网站"

    except Exception as e:
        return f"❌ 打开网站失败: {e}"

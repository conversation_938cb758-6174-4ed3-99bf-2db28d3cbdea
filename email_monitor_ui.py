#!/usr/bin/env python3
"""
邮箱监控窗口UI类 - 使用PyQt6
分离界面逻辑和业务逻辑
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                             QPushButton, QLabel, QLineEdit, QTextEdit, QCheckBox,
                             QGroupBox)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QClipboard
import random
import string


# 样式常量
GREEN_BUTTON_STYLE = """
    QPushButton {
        background-color: #4CAF50;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
    }
    QPushButton:hover {
        background-color: #45a049;
    }
    QPushButton:pressed {
        background-color: #388e3c;
    }
"""

RED_BUTTON_STYLE = """
    QPushButton {
        background-color: #f44336;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
    }
    QPushButton:hover {
        background-color: #d32f2f;
    }
    QPushButton:pressed {
        background-color: #b71c1c;
    }
"""


class EmailMonitorUI(QDialog):
    """邮箱监控窗口UI类"""
    
    # 定义信号
    start_monitoring = pyqtSignal(str, str)  # 邮箱账号, 密码
    stop_monitoring = pyqtSignal()
    clear_log_clicked = pyqtSignal()
    save_login_clicked = pyqtSignal()  # 保存登录信息
    copy_code_clicked = pyqtSignal()
    open_website_clicked = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.monitoring = False
        self.setup_ui()
        self.generate_random_account()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("📧 邮箱验证码监控器")
        self.setMinimumSize(700, 800)
        self.resize(750, 850)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # 创建标题区域
        self.create_title_section(main_layout)
        
        # 创建登录信息区域
        self.create_login_section(main_layout)
        
        # 创建控制按钮区域
        self.create_control_section(main_layout)
        
        # 创建状态区域
        self.create_status_section(main_layout)
        
        # 创建日志区域
        self.create_log_section(main_layout)
        
        # 创建验证码结果区域
        self.create_result_section(main_layout)
        
    def create_title_section(self, parent_layout):
        """创建标题区域"""
        title_layout = QVBoxLayout()
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 主标题
        title_label = QLabel("📧 邮箱验证码监控器")
        title_font = QFont("Microsoft YaHei", 16, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2196F3; margin-bottom: 5px;")
        
        # 副标题
        subtitle_label = QLabel("自动监控邮箱并提取验证码")
        subtitle_font = QFont("Microsoft YaHei", 10)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #666666; margin-bottom: 20px;")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        
        parent_layout.addLayout(title_layout)
        
    def create_login_section(self, parent_layout):
        """创建登录信息区域"""
        login_group = QGroupBox("📝 登录信息")
        login_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        login_layout = QGridLayout(login_group)
        login_layout.setSpacing(15)
        
        # 邮箱账号
        email_label = QLabel("📧 邮箱账号:")
        email_label.setFont(QFont("Microsoft YaHei", 10))
        self.email_input = QLineEdit()
        self.email_input.setMinimumHeight(35)
        self.email_input.setFont(QFont("Microsoft YaHei", 10))
        self.email_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
        """)
        
        # 随机账户
        random_label = QLabel("🎲 随机账户:")
        random_label.setFont(QFont("Microsoft YaHei", 10))
        self.random_account_input = QLineEdit()
        self.random_account_input.setMinimumHeight(35)
        self.random_account_input.setFont(QFont("Microsoft YaHei", 10))
        self.random_account_input.setReadOnly(True)
        self.random_account_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px 10px;
                background-color: #f5f5f5;
            }
        """)
        
        # 随机账户按钮
        self.refresh_random_button = QPushButton("🔄 刷新")
        self.refresh_random_button.setMinimumHeight(35)
        self.refresh_random_button.clicked.connect(self.generate_random_account)
        
        self.copy_random_button = QPushButton("📋 复制")
        self.copy_random_button.setMinimumHeight(35)
        self.copy_random_button.clicked.connect(self.copy_random_account)
        
        # 邮箱密码
        password_label = QLabel("🔒 邮箱密码:")
        password_label.setFont(QFont("Microsoft YaHei", 10))
        self.password_input = QLineEdit()
        self.password_input.setMinimumHeight(35)
        self.password_input.setFont(QFont("Microsoft YaHei", 10))
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #ddd;
                border-radius: 5px;
                padding: 5px 10px;
            }
            QLineEdit:focus {
                border-color: #2196F3;
            }
        """)
        
        # 选项区域
        options_layout = QHBoxLayout()
        
        # 保存登录信息按钮
        self.save_login_button = QPushButton("💾 保存登录信息")
        self.save_login_button.setFont(QFont("Microsoft YaHei", 10))
        self.save_login_button.clicked.connect(self.on_save_login)
        
        # 自动复制选项
        self.auto_copy_checkbox = QCheckBox("📋 自动复制验证码")
        self.auto_copy_checkbox.setFont(QFont("Microsoft YaHei", 9))
        self.auto_copy_checkbox.setChecked(True)
        
        options_layout.addWidget(self.save_login_button)
        options_layout.addStretch()
        options_layout.addWidget(self.auto_copy_checkbox)
        
        # 添加到网格布局
        login_layout.addWidget(email_label, 0, 0)
        login_layout.addWidget(self.email_input, 0, 1, 1, 3)
        
        login_layout.addWidget(random_label, 1, 0)
        login_layout.addWidget(self.random_account_input, 1, 1)
        login_layout.addWidget(self.refresh_random_button, 1, 2)
        login_layout.addWidget(self.copy_random_button, 1, 3)
        
        login_layout.addWidget(password_label, 2, 0)
        login_layout.addWidget(self.password_input, 2, 1, 1, 3)
        
        login_layout.addLayout(options_layout, 3, 0, 1, 4)
        
        parent_layout.addWidget(login_group)
        
    def create_control_section(self, parent_layout):
        """创建控制按钮区域"""
        control_layout = QHBoxLayout()
        control_layout.setSpacing(15)
        
        # 开始/停止监控按钮
        self.start_button = QPushButton("🚀 开始监控")
        self.start_button.setMinimumHeight(45)
        self.start_button.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))
        self.start_button.setStyleSheet(GREEN_BUTTON_STYLE)
        self.start_button.clicked.connect(self.toggle_monitoring)
        
        # 清空日志按钮
        self.clear_button = QPushButton("🗑️ 清空日志")
        self.clear_button.setMinimumHeight(45)
        self.clear_button.setFont(QFont("Microsoft YaHei", 11))
        self.clear_button.clicked.connect(self.clear_log_clicked.emit)
        
        # 打开网站按钮
        self.website_button = QPushButton("🌐 打开网站")
        self.website_button.setMinimumHeight(45)
        self.website_button.setFont(QFont("Microsoft YaHei", 11))
        self.website_button.clicked.connect(self.open_website_clicked.emit)
        
        control_layout.addWidget(self.start_button)
        control_layout.addWidget(self.clear_button)
        control_layout.addWidget(self.website_button)
        
        parent_layout.addLayout(control_layout)
        
    def create_status_section(self, parent_layout):
        """创建状态区域"""
        self.status_label = QLabel("🟢 就绪")
        self.status_label.setFont(QFont("Microsoft YaHei", 10))
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 8px;
            }
        """)
        
        parent_layout.addWidget(self.status_label)

    def create_log_section(self, parent_layout):
        """创建日志区域"""
        log_group = QGroupBox("📋 监控日志")
        log_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
            }
        """)

        log_layout.addWidget(self.log_text)
        parent_layout.addWidget(log_group)

    def create_result_section(self, parent_layout):
        """创建验证码结果区域"""
        result_group = QGroupBox("🎯 验证码结果")
        result_group.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        result_layout = QHBoxLayout(result_group)
        result_layout.setSpacing(15)

        # 验证码标签
        code_label = QLabel("🔢 验证码:")
        code_label.setFont(QFont("Microsoft YaHei", 11, QFont.Weight.Bold))

        # 验证码输入框
        self.code_input = QLineEdit()
        self.code_input.setMinimumHeight(40)
        self.code_input.setFont(QFont("Consolas", 16, QFont.Weight.Bold))
        self.code_input.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.code_input.setReadOnly(True)
        self.code_input.setStyleSheet("""
            QLineEdit {
                border: 2px solid #4CAF50;
                border-radius: 8px;
                padding: 8px;
                background-color: #f8f8f8;
                color: #2e7d32;
            }
        """)

        # 复制按钮
        self.copy_code_button = QPushButton("📋 复制")
        self.copy_code_button.setMinimumHeight(40)
        self.copy_code_button.setFont(QFont("Microsoft YaHei", 10, QFont.Weight.Bold))
        self.copy_code_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        self.copy_code_button.clicked.connect(self.copy_code_clicked.emit)

        result_layout.addWidget(code_label)
        result_layout.addWidget(self.code_input, 1)  # 设置拉伸因子
        result_layout.addWidget(self.copy_code_button)

        parent_layout.addWidget(result_group)

    def generate_random_account(self):
        """生成随机邮箱账户"""
        current_account = self.email_input.text().strip()

        if not current_account:
            base_account = "user"
        else:
            if "@" in current_account:
                base_account = current_account.split("@")[0]
            else:
                base_account = current_account

        random_letters = ''.join(random.choices(string.ascii_lowercase, k=6))
        random_account = f"{base_account}{random_letters}@2925.com"
        self.random_account_input.setText(random_account)
        return random_account

    def copy_random_account(self):
        """复制随机邮箱账户到剪贴板"""
        account = self.random_account_input.text().strip()
        if account:
            clipboard = QClipboard()
            clipboard.setText(account)
            self.append_log(f"📋 随机邮箱账户已复制到剪贴板: {account}")
        else:
            self.append_log("⚠️ 没有随机邮箱账户可复制", "WARNING")

    def on_save_login(self):
        """保存登录信息"""
        email = self.email_input.text().strip()
        password = self.password_input.text().strip()

        if not email:
            self.append_log("⚠️ 请输入邮箱账号", "WARNING")
            return

        if not password:
            self.append_log("⚠️ 请输入邮箱密码", "WARNING")
            return

        self.save_login_clicked.emit()

    def toggle_monitoring(self):
        """切换监控状态"""
        if not self.monitoring:
            email = self.email_input.text().strip()
            password = self.password_input.text().strip()

            if not email or not password:
                self.append_log("❌ 请输入邮箱账号和密码！", "ERROR")
                return

            self.start_monitoring_ui()
            self.start_monitoring.emit(email, password)
        else:
            self.stop_monitoring_ui()
            self.stop_monitoring.emit()

    def start_monitoring_ui(self):
        """开始监控时的UI更新"""
        self.monitoring = True
        self.start_button.setText("⏹️ 停止监控")
        self.start_button.setStyleSheet(RED_BUTTON_STYLE)
        self.email_input.setEnabled(False)
        self.password_input.setEnabled(False)
        self.auto_copy_checkbox.setEnabled(False)
        self.save_login_button.setEnabled(False)
        self.status_label.setText("🔄 正在启动监控...")
        self.code_input.clear()

    def stop_monitoring_ui(self):
        """停止监控时的UI更新"""
        self.monitoring = False
        self.start_button.setText("🚀 开始监控")
        self.start_button.setStyleSheet(GREEN_BUTTON_STYLE)
        self.email_input.setEnabled(True)
        self.password_input.setEnabled(True)
        self.auto_copy_checkbox.setEnabled(True)
        self.save_login_button.setEnabled(True)
        self.status_label.setText("🔴 监控已停止")

    def append_log(self, message, level="INFO"):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据日志级别设置颜色
        color_map = {
            "INFO": "#ffffff",
            "SUCCESS": "#4CAF50",
            "WARNING": "#FF9800",
            "ERROR": "#f44336"
        }
        color = color_map.get(level, "#ffffff")

        log_message = f'<span style="color: {color}">[{timestamp}] {message}</span><br>'
        self.log_text.insertHtml(log_message)

        # 滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        # 如果是验证码消息，提取并显示
        if message.startswith("VERIFICATION_CODE:"):
            code = message.split(":", 1)[1]
            self.code_input.setText(code)

            # 自动复制到剪贴板
            if self.auto_copy_checkbox.isChecked():
                try:
                    clipboard = QClipboard()
                    clipboard.setText(code)
                    self.status_label.setText(f"✅ 验证码已获取并复制到剪贴板: {code}")
                    copy_timestamp = datetime.now().strftime("%H:%M:%S")
                    copy_message = f'<span style="color: #4CAF50">[{copy_timestamp}] ✅ 验证码已自动复制到剪贴板</span><br>'
                    self.log_text.insertHtml(copy_message)
                    scrollbar = self.log_text.verticalScrollBar()
                    scrollbar.setValue(scrollbar.maximum())
                except Exception:
                    self.status_label.setText(f"⚠️ 验证码已获取: {code} (复制失败)")
            else:
                self.status_label.setText(f"✅ 验证码已获取: {code}")

            # 自动停止监控
            self.stop_monitoring_ui()
            self.stop_monitoring.emit()

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.code_input.clear()

    def set_email_account(self, email):
        """设置邮箱账号"""
        self.email_input.setText(email)

    def set_email_password(self, password):
        """设置邮箱密码"""
        self.password_input.setText(password)

    def get_email_account(self):
        """获取邮箱账号"""
        return self.email_input.text().strip()

    def get_email_password(self):
        """获取邮箱密码"""
        return self.password_input.text().strip()

    def is_auto_copy_enabled(self):
        """是否启用自动复制"""
        return self.auto_copy_checkbox.isChecked()

    def set_auto_copy_enabled(self, enabled):
        """设置自动复制状态"""
        self.auto_copy_checkbox.setChecked(enabled)

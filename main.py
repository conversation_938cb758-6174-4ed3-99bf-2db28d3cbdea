#!/usr/bin/env python3
"""
Augment VIP - PyQt6版本
用于管理和清理VS Code数据库中与Augment相关的条目

功能:
- 清理VS Code数据库中的Augment相关条目
- 修改VS Code遥测ID以保护隐私
- 可视化GUI界面（PyQt6）
- 邮箱验证码监控
- 仅支持Windows系统
"""

import sys
import threading
import queue
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import QThread, pyqtSignal, QTimer

# 导入UI模块
from main_window_ui import MainWindowUI
from email_monitor_ui import EmailMonitorUI

# 导入核心功能模块
from core_functions import (
    clean_vscode_db, modify_telemetry_ids, ConfigManager,
    email_monitor_main, open_website_in_chrome, EMAIL_AVAILABLE,
    __version__
)


class EmailMonitorThread(QThread):
    """邮箱监控线程"""
    
    message_received = pyqtSignal(str)
    
    def __init__(self, email_account, email_password):
        super().__init__()
        self.email_account = email_account
        self.email_password = email_password
        self.output_queue = queue.Queue()
        self.running = True
        
    def run(self):
        """运行邮箱监控"""
        email_monitor_main(self.email_account, self.email_password, self.output_queue)
        
    def stop(self):
        """停止监控"""
        self.running = False
        self.output_queue.put('STOP')
        
    def get_messages(self):
        """获取消息队列中的消息"""
        messages = []
        try:
            while True:
                message = self.output_queue.get_nowait()
                if message == 'STOP':
                    break
                messages.append(message)
        except queue.Empty:
            pass
        return messages


class EmailMonitorWindow(EmailMonitorUI):
    """邮箱监控窗口控制器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.monitor_thread = None
        self.message_timer = QTimer()
        self.message_timer.timeout.connect(self.check_messages)
        
        # 连接信号
        self.start_monitoring.connect(self.on_start_monitoring)
        self.stop_monitoring.connect(self.on_stop_monitoring)
        self.clear_log_clicked.connect(self.clear_log)
        self.save_login_clicked.connect(self.on_save_login)
        self.copy_code_clicked.connect(self.on_copy_code)
        self.open_website_clicked.connect(self.on_open_website)
        
        # 加载配置
        self.load_saved_config()
        
        # 初始化日志
        self.append_log("欢迎使用邮箱验证码监控器")
        self.append_log("请输入邮箱账号和密码，然后点击'开始监控'")
        
    def load_saved_config(self):
        """加载保存的配置"""
        config = self.config_manager.load_config()
        self.set_email_account(config['account'])
        self.set_email_password(config['password'])
        self.set_auto_copy_enabled(config['auto_copy'])
        
    def on_start_monitoring(self, email_account, email_password):
        """开始监控"""
        if not EMAIL_AVAILABLE:
            QMessageBox.critical(self, "错误", "缺少邮箱相关依赖包！\n请安装: pip install poplib beautifulsoup4")
            return
            
        self.append_log(f"开始监控邮箱: {email_account}")
        
        # 创建并启动监控线程
        self.monitor_thread = EmailMonitorThread(email_account, email_password)
        self.monitor_thread.start()
        
        # 启动消息检查定时器
        self.message_timer.start(100)  # 每100毫秒检查一次
        
    def on_stop_monitoring(self):
        """停止监控"""
        if self.monitor_thread:
            self.monitor_thread.stop()
            self.monitor_thread = None
            
        self.message_timer.stop()
        self.append_log("监控已停止")
        
    def check_messages(self):
        """检查消息队列"""
        if self.monitor_thread:
            messages = self.monitor_thread.get_messages()
            for message in messages:
                self.append_log(message)
                # 如果收到验证码消息，立即停止处理其他消息
                if message.startswith("VERIFICATION_CODE:"):
                    break
                
    def on_save_login(self):
        """保存登录信息"""
        email_account = self.get_email_account()
        email_password = self.get_email_password()
        auto_copy = self.is_auto_copy_enabled()
        if self.config_manager.save_config(email_account, email_password, auto_copy):
            self.append_log("✅ 登录信息已保存到系统，下次打开时将自动填入", "SUCCESS")
        else:
            self.append_log("❌ 保存登录信息失败", "ERROR")
            
    def on_copy_code(self):
        """复制验证码"""
        code = self.code_input.text()
        if code:
            clipboard = QApplication.clipboard()
            clipboard.setText(code)
            self.append_log(f"📋 验证码已复制到剪贴板: {code}")
        else:
            self.append_log("⚠️ 没有验证码可复制", "WARNING")
            
    def on_open_website(self):
        """打开网站"""
        result = open_website_in_chrome()
        self.append_log(result)


class MainWindow(MainWindowUI):
    """主窗口控制器"""
    
    def __init__(self):
        super().__init__()
        
        # 连接信号
        self.reset_clicked.connect(self.on_reset_clicked)
        self.email_clicked.connect(self.on_email_clicked)
        self.clear_log_clicked.connect(self.clear_log)
        
        # 初始化日志
        self.append_log("欢迎使用 Augment VIP v" + __version__)
        self.append_log("点击'一键重置'开始操作")
        
    def on_reset_clicked(self):
        """一键重置功能"""
        # 确认对话框
        reply = QMessageBox.question(
            self, 
            "确认操作",
            "此操作将：\n\n• 清理VS Code数据库中的Augment相关条目\n• 生成新的随机遥测ID\n• 自动备份所有修改的文件\n\n确定要继续吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.append_log("开始一键重置操作...")
            self.run_reset_in_thread()
            
    def run_reset_in_thread(self):
        """在后台线程中运行重置操作"""
        def reset_operation():
            success_count = 0
            total_operations = 2
            
            # 执行数据库清理
            self.append_log("正在清理VS Code数据库...")
            if clean_vscode_db(self.append_log):
                success_count += 1
                
            # 执行遥测ID修改
            self.append_log("正在修改遥测ID...")
            if modify_telemetry_ids(self.append_log):
                success_count += 1
                
            # 显示结果
            if success_count == total_operations:
                self.append_log("一键重置完成！所有操作成功执行", "SUCCESS")
                QMessageBox.information(self, "操作完成", "一键重置完成！\n\n请重启VS Code以使更改生效。")
            elif success_count > 0:
                self.append_log(f"部分操作完成 ({success_count}/{total_operations})", "WARNING")
                QMessageBox.warning(self, "部分完成", f"部分操作完成 ({success_count}/{total_operations})\n\n请检查日志了解详情。")
            else:
                self.append_log("一键重置失败", "ERROR")
                QMessageBox.critical(self, "操作失败", "一键重置失败！\n\n请检查日志了解详情。")
        
        # 在新线程中运行
        thread = threading.Thread(target=reset_operation)
        thread.daemon = True
        thread.start()
        
    def on_email_clicked(self):
        """打开邮箱接收工具"""
        try:
            self.append_log("正在启动邮箱接收工具...", "INFO")
            
            # 创建邮箱监控窗口
            self.email_window = EmailMonitorWindow(self)
            self.email_window.show()
            
            self.append_log("邮箱接收工具已启动", "SUCCESS")
            
        except Exception as e:
            self.append_log(f"启动邮箱接收工具失败: {e}", "ERROR")
            QMessageBox.critical(self, "错误", f"启动邮箱接收工具失败: {e}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("Augment VIP")
    app.setApplicationVersion(__version__)
    app.setOrganizationName("Augment Code")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
主窗口UI类 - 使用PyQt6
分离界面逻辑和业务逻辑
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QTextEdit, QSizePolicy)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont


class MainWindowUI(QMainWindow):
    """主窗口UI类"""
    
    # 定义信号
    reset_clicked = pyqtSignal()
    email_clicked = pyqtSignal()
    clear_log_clicked = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("🚀 Augment VIP v2.0.0")
        self.setMinimumSize(500, 400)
        self.resize(600, 500)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # 创建标题区域
        self.create_title_section(main_layout)
        
        # 创建按钮区域
        self.create_button_section(main_layout)
        
        # 创建日志区域
        self.create_log_section(main_layout)
        
        # 创建底部按钮区域
        self.create_bottom_section(main_layout)
        
    def create_title_section(self, parent_layout):
        """创建标题区域"""
        title_layout = QVBoxLayout()
        title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 主标题
        title_label = QLabel("🚀 Augment VIP v2.0.0")
        title_font = QFont("Microsoft YaHei", 18, QFont.Weight.Bold)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2196F3; margin-bottom: 10px;")
        
        # 副标题
        subtitle_label = QLabel("VS Code 一键重置工具")
        subtitle_font = QFont("Microsoft YaHei", 12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("color: #666666; margin-bottom: 30px;")
        
        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)
        
        parent_layout.addLayout(title_layout)
        
    def create_button_section(self, parent_layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(20)
        
        # 一键重置按钮
        self.reset_button = QPushButton("🔄 一键重置")
        self.reset_button.setMinimumHeight(50)
        self.reset_button.setFont(QFont("Microsoft YaHei", 11))
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        self.reset_button.clicked.connect(self.reset_clicked.emit)
        
        # 邮箱接收按钮
        self.email_button = QPushButton("📧 邮箱接收")
        self.email_button.setMinimumHeight(50)
        self.email_button.setFont(QFont("Microsoft YaHei", 11))
        self.email_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #388e3c;
            }
        """)
        self.email_button.clicked.connect(self.email_clicked.emit)
        
        button_layout.addWidget(self.reset_button)
        button_layout.addWidget(self.email_button)
        
        parent_layout.addLayout(button_layout)
        
    def create_log_section(self, parent_layout):
        """创建日志区域"""
        # 日志标题
        log_label = QLabel("📋 操作日志")
        log_label.setFont(QFont("Microsoft YaHei", 12, QFont.Weight.Bold))
        log_label.setStyleSheet("color: #333333; margin-bottom: 10px;")
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #2b2b2b;
                color: #ffffff;
                border: 1px solid #555555;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        
        # 设置日志区域可以扩展
        self.log_text.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        
        parent_layout.addWidget(log_label)
        parent_layout.addWidget(self.log_text)
        
    def create_bottom_section(self, parent_layout):
        """创建底部按钮区域"""
        bottom_layout = QHBoxLayout()
        bottom_layout.setAlignment(Qt.AlignmentFlag.AlignLeft)
        
        # 清空日志按钮
        self.clear_button = QPushButton("🗑️ 清空日志")
        self.clear_button.setFont(QFont("Microsoft YaHei", 10))
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #E65100;
            }
        """)
        self.clear_button.clicked.connect(self.clear_log_clicked.emit)
        
        bottom_layout.addWidget(self.clear_button)
        bottom_layout.addStretch()  # 添加弹性空间
        
        parent_layout.addLayout(bottom_layout)
        
    def append_log(self, message, level="INFO"):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 根据日志级别设置颜色
        color_map = {
            "INFO": "#ffffff",
            "SUCCESS": "#4CAF50", 
            "WARNING": "#FF9800",
            "ERROR": "#f44336"
        }
        color = color_map.get(level, "#ffffff")
        
        log_message = f'<span style="color: {color}">[{timestamp}] {message}</span><br>'
        self.log_text.insertHtml(log_message)
        
        # 滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()

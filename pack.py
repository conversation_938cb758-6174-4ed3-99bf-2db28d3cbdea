#!/usr/bin/env python3
"""
Augment VIP 打包脚本
"""

import os
import sys
import subprocess
import shutil

def main():
    print("🔧 Augment VIP 打包工具")
    print("=" * 30)

    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"[信息] PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("[信息] 正在安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])

    # 清理旧文件
    for folder in ["dist", "build"]:
        if os.path.exists(folder):
            print(f"[信息] 清理 {folder} 目录...")
            shutil.rmtree(folder)

    # 打包
    print("[信息] 开始打包...")
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed",
        "--name", "AugmentVIP",
        "main.py"
    ]

    try:
        subprocess.check_call(cmd)
        print("[成功] 打包完成！")

        # 清理构建临时文件
        print("[信息] 清理构建临时文件...")

        # 删除build目录
        if os.path.exists("build"):
            shutil.rmtree("build")
            print("[信息] 已删除 build 目录")

        # 删除spec文件
        spec_files = [f for f in os.listdir(".") if f.endswith(".spec")]
        for spec_file in spec_files:
            os.remove(spec_file)
            print(f"[信息] 已删除 {spec_file}")

        # 删除__pycache__目录
        if os.path.exists("__pycache__"):
            shutil.rmtree("__pycache__")
            print("[信息] 已删除 __pycache__ 目录")

        # 删除其他临时文件
        temp_files = [f for f in os.listdir(".") if f.endswith((".pyc", ".pyo"))]
        for temp_file in temp_files:
            os.remove(temp_file)
            print(f"[信息] 已删除 {temp_file}")

        print("\n[成功] 清理完成！")
        print("输出文件: dist/AugmentVIP.exe")
        print("\n保留的源代码文件:")
        print("  ✓ main.py - 主程序源码")
        print("  ✓ main_window_ui.py - 主窗口UI")
        print("  ✓ email_monitor_ui.py - 邮箱监控UI")
        print("  ✓ core_functions.py - 核心功能")
        print("  ✓ pack.py - 打包脚本")
        print("  ✓ requirements.txt - 依赖列表")
        print("\n📦 打包内容:")
        print("  ✓ 一键重置功能")
        print("  ✓ 邮箱验证码监控")
        print("  ✓ PyQt6 GUI界面")
        print("  ✓ VS Code数据库清理")
        print("  ✓ 遥测ID修改")
        print("\n🚀 使用方法:")
        print("  双击 dist/AugmentVIP.exe 启动程序")

    except subprocess.CalledProcessError:
        print("[错误] 打包失败")
        sys.exit(1)

    input("按回车键退出...")

if __name__ == "__main__":
    main()
